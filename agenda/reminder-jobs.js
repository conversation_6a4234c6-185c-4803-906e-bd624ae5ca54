/* eslint-disable max-len */
const { Platform } = require("../models")
const mail = require("../lib/mail")

/**
 * Send profile completion reminder emails to users
 * This job will check for platforms that have recently changed to launch status 1 or 2
 * and send a reminder email to their owners the next day
 * Emails are only sent in production environment
 */
module.exports = (agenda) => {
  // Define the job to check for platforms that need reminders
  agenda.define("check-platform-status", async () => {
    try {
      // Find all platforms with launch status 1 or 2 that were updated in the last 24 hours
      const oneDayAgo = new Date()
      oneDayAgo.setDate(oneDayAgo.getDate() - 1)

      const platforms = await Platform.find({
        launchStatus: { $in: [1, 2] },
        updatedAt: { $gte: oneDayAgo }
      }).lean()

      // Schedule reminder emails for each platform for tomorrow
      if (platforms.length > 0) {
        // Schedule the reminder job for tomorrow at 10:00 AM
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        tomorrow.setHours(10, 0, 0, 0)

        // Store platform IDs for the reminder job
        const platformIds = platforms.map(platform => platform._id.toString())

        await agenda.schedule(tomorrow, "send-profile-reminder", { platformIds })
      }

      // Schedule the next check for tomorrow
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(9, 0, 0, 0) // Run at 9:00 AM, before the reminder job

      await agenda.schedule(tomorrow, "check-platform-status")
    } catch (error) {
      // Even if there's an error, schedule the next check
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(9, 0, 0, 0)

      await agenda.schedule(tomorrow, "check-platform-status")
    }
  })

  // Define the job to send reminder emails
  agenda.define("send-profile-reminder", async (job) => {
    try {
      const { platformIds } = job.attrs.data || { platformIds: [] }

      if (!platformIds || platformIds.length === 0) {
        return
      }

      // Only proceed with sending emails in production environment
      if (process.env.NODE_ENV !== "production") {
        return
      }

      // Find platforms with their primary users
      const platforms = await Platform.find({
        _id: { $in: platformIds }
      }).populate("primaryUser", "email name").lean()

      // Send reminder email to each platform owner
      platforms.forEach(platform => {
        if (!platform.primaryUser || !platform.primaryUser.email) {
          return;
        }

        const user = platform.primaryUser;

        // Get user's first name
        const firstName = user.name && user.name.first ? user.name.first : "User";

        // Send the actual email without awaiting
        mail("profile-reminder", {
          to: user.email,
          subject: "Let's Get Your Platform Ready 🎯",
          locals: {
            firstName,
            url: `${process.env.SITE_URL}/login`,
            platform: "Vewmee White Level"
          }
        });
      });
    } catch (error) {
      // Handle error silently
    }
  })

  // Schedule the initial check job to run immediately
  agenda.on("ready", async () => {
    // Cancel any existing jobs with these names
    await agenda.cancel({ name: "check-platform-status" })
    await agenda.cancel({ name: "send-profile-reminder" })

    // Schedule the check job to run immediately
    await agenda.now("check-platform-status")
  })
}
