/* eslint-disable consistent-return */
/* eslint-disable max-len */
/* eslint-disable import/no-unresolved */
const jwt = require("jsonwebtoken")
const axios = require("axios")
const { User, Platform } = require("../../../models")
const stripeService = require("../../../lib/stripe")
const mail = require("../../../lib/mail")

module.exports = {
  /**
   * @swagger
   * /signup:
   *   post:
   *     summary: User registration (Owner account)
   *     description: Register a new user with owner privileges. Creates a Stripe customer account and a new platform. The user is automatically activated (isActive=true).
   *     tags: [Auth]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *               - name
   *               - password
   *               - phone
   *               - address
   *             properties:
   *               email:
   *                 type: string
   *                 format: email
   *                 description: User's email address (must be unique for Owner accounts)
   *               name:
   *                 type: string
   *                 description: User's full name (will be split into first and last name)
   *               password:
   *                 type: string
   *                 format: password
   *                 description: User's password (will be hashed before storage)
   *               phone:
   *                 type: string
   *                 description: User's phone number
   *               address:
   *                 type: string
   *                 description: User's address
   *           examples:
   *             basic_signup:
   *               summary: Basic signup example
   *               value:
   *                 email: "<EMAIL>"
   *                 phone: "**********"
   *                 name: "John Doe"
   *                 address: "123 Main St, City, Country"
   *                 password: "securePassword123"
   *             business_signup:
   *               summary: Business signup example
   *               value:
   *                 email: "<EMAIL>"
   *                 phone: "9876543210"
   *                 name: "Jane Smith"
   *                 address: "456 Business Ave, Suite 100, City, Country"
   *                 password: "BusinessPass789!"
   *     responses:
   *       200:
   *         description: Registration successful
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         phone:
   *                           type: string
   *                           example: "**********"
   *                         address:
   *                           type: string
   *                           example: "123 Main St, City, Country"
   *                         stripeId:
   *                           type: string
   *                           example: "cus_Abc123XyZ456"
   *                           description: Stripe customer ID (may be absent if Stripe account creation failed)
   *                         isOwner:
   *                           type: boolean
   *                           example: true
   *                           description: Always true for regular signup (creates Owner account)
   *                         platformId:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                           description: ID of the created platform
   *                         platforms:
   *                           type: array
   *                           description: Array of platform IDs associated with the user
   *                           items:
   *                             type: string
   *                           example: ["60d21b4667d0d8992e610c85"]
   *                     token:
   *                       type: string
   *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                       description: JWT authentication token valid for 30 days (3600 * 24 * 30 seconds)
   *                     welcomeEmail:
   *                       type: object
   *                       description: Information about the welcome email (only in development environment)
   *                       properties:
   *                         sent:
   *                           type: boolean
   *                           example: false
   *                           description: Whether the welcome email was sent
   *                         message:
   *                           type: string
   *                           example: "Development mode: Welcome email not sent"
   *                           description: Status message about the welcome email
   *                         loginDetails:
   *                           type: object
   *                           description: Login details (only in development environment)
   *                           properties:
   *                             email:
   *                               type: string
   *                               example: "<EMAIL>"
   *                             password:
   *                               type: string
   *                               example: "securePassword123"
   *                             loginUrl:
   *                               type: string
   *                               example: "http://localhost:3000/login"
   *             examples:
   *               success_response:
   *                 summary: Successful registration
   *                 value:
   *                   error: false
   *                   data:
   *                     user:
   *                       email: "<EMAIL>"
   *                       name:
   *                         first: "John"
   *                         last: "Doe"
   *                       phone: "**********"
   *                       address: "123 Main St, City, Country"
   *                       stripeId: "cus_Abc123XyZ456"
   *                       isOwner: true
   *                       platformId: "60d21b4667d0d8992e610c85"
   *                       platforms: ["60d21b4667d0d8992e610c85"]
   *                     token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                     welcomeEmail:
   *                       sent: false
   *                       message: "Development mode: Welcome email not sent"
   *                       loginDetails:
   *                         email: "<EMAIL>"
   *                         password: "securePassword123"
   *                         loginUrl: "http://localhost:3000/login"
   *
   *       400:
   *         description: Bad request - missing required fields or email already exists
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *             examples:
   *               missing_email:
   *                 summary: Missing email
   *                 value:
   *                   error: true
   *                   reason: "email is required"
   *               missing_name:
   *                 summary: Missing name
   *                 value:
   *                   error: true
   *                   reason: "name is required"
   *               missing_phone:
   *                 summary: Missing phone
   *                 value:
   *                   error: true
   *                   reason: "Phone number is required"
   *               missing_password:
   *                 summary: Missing password
   *                 value:
   *                   error: true
   *                   reason: "password is required"
   *               missing_address:
   *                 summary: Missing address
   *                 value:
   *                   error: true
   *                   reason: "Address is required"
   *               email_exists:
   *                 summary: Email already exists
   *                 value:
   *                   error: true
   *                   reason: "Email already exists"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   */
  async post(req, res) {
    try {
      const {
        name, email, phone, password, address
      } = req.body

      // Validate required fields
      if (!email) {
        return res
          .status(400)
          .json({ error: true, reason: "email is required" })
      }

      if (!name) {
        return res
          .status(400)
          .json({ error: true, reason: "name is required" })
      }

      if (!phone) {
        return res
          .status(400)
          .json({ error: true, reason: "Phone number is required" })
      }

      if (!password) {
        return res
          .status(400)
          .json({ error: true, reason: "password is required" })
      }

      if (!address) {
        return res
          .status(400)
          .json({ error: true, reason: "Address is required" })
      }
      // Split name into first and last names
      const [first, ...last] = name.split(" ")
      const lastName = last.join(" ")
      await User.countDocuments({ email, userType: "Owner" }).then((count) => {
        if (count > 0) {
          return res.status(400).json({
            error: true,
            reason: "Email already exists"
          })
        }
      })
      // Create user with basic information
      let user = await User.create({
        email,
        phone,
        password,
        name: {
          first,
          last: lastName,
        },
        address,
        isActive: true, // Activate user by default
        userType: "Owner"
      })

      // Create Stripe customer account
      try {
        const stripeCustomer = await stripeService.createAccount(email)
        user.stripeId = stripeCustomer.id
        await user.save()
      } catch (stripeError) {
        console.error("Stripe account creation error:", stripeError)
        // Don't fail the signup if Stripe account creation fails
        // The user can retry Stripe setup later
      }

      // Create vendor account in the vendor Stripe account
      // try {
      //   const vendorAccount = await stripeVendorService.createVendorAccount(email, process.env.BUSINESS_COUNTRY)
      //   user.vendorStripeId = vendorAccount.id // Save the vendor account ID
      // } catch (vendorError) {
      //   console.error("Vendor account creation error:", vendorError)
      //   // Don't fail the signup if vendor account creation fails
      // }

      const platform = await Platform.create({
        primaryUser: user._id
      })

      user.platforms = [platform._id]
      await user.save()
      // Convert to object and remove sensitive information
      user = user.toObject()
      delete user.password
      delete user.forgotpassword

      const payload = {
        id: user._id,
        _id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        stripeId: user.stripeId,
        userType: user.userType
      }

      const token = jwt.sign(payload, process.env.SECRET, {
        expiresIn: 3600 * 24 * 30, // 1 month
      })

      // Prepare user data
      const userData = {
        email: user.email,
        name: user.name,
        phone: user.phone,
        stripeId: user.stripeId,
        address: user.address,
        isOwner: true, // Always true for regular signup (creates Owner account)
        platformId: platform._id,
        platforms: user.platforms
      }

      // Prepare response object
      const response = {
        error: false,
        data: {
          user: userData,
          token
        }
      }

      // Send welcome email with login details
      const loginUrl = `${process.env.SITE_URL}/login`

      // In development environment, don't send email, just return the login details
      if (process.env.NODE_ENV === "development") {
        response.data.welcomeEmail = {
          sent: false,
          message: "Development mode: Welcome email not sent",
          loginDetails: {
            email: user.email,
            loginUrl
          }
        }
      } else {
        // In production, send the welcome email
        try {
          await mail("welcome", {
            to: user.email,
            subject: "Welcome to Your Creator HQ – Let’s Build Something Amazing 🚀",
            locals: {
              firstName: user.name.first,
              user_email: user.email,
              login_link: loginUrl,
              platformName: "Vewmee White Level"
            }
          })
          console.log(`Welcome email sent to ${user.email}`)
        } catch (emailError) {
          console.error("Error sending welcome email:", emailError)
          // Continue execution even if email fails
        }

        // No email response information added to the API response
      }

      return res.status(200).json(response)
    } catch (err) {
      return res.status(500).json({ error: true, reason: err.message })
    }
  },

  /**
   * @swagger
   * /signup/google:
   *   post:
   *     summary: Google Sign Up or Login
   *     description: Signs up a new user with Google or logs in an existing user. For new users, creates a Stripe customer account and sets isOwner=true.
   *     tags: [Auth]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - access_token
   *             properties:
   *               access_token:
   *                 type: string
   *                 description: Google OAuth access token obtained from Google Sign-In
   *           example:
   *             access_token: "ya29.a0AfB_byABCDEFGHIJKLMNOPQRSTUVWXYZ**********abcdefghijklmnopqrstuvwxyz"
   *     responses:
   *       200:
   *         description: Google signup or login successful
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         phone:
   *                           type: string
   *                           example: null
   *                           description: May be null for Google signups
   *                         stripeId:
   *                           type: string
   *                           example: "cus_Abc123XyZ456"
   *                           description: Stripe customer ID
   *                         isOwner:
   *                           type: boolean
   *                           example: true
   *                           description: Indicates if the user is an owner or team member
   *                         platformId:
   *                           type: string
   *                           example: "6821db2a665e6d64674ee897"
   *                           description: Only included for owner users with platforms
   *                         ownerId:
   *                           type: string
   *                           example: "6821db2a665e6d64674ee897"
   *                           description: Only included for team members, references the owner's ID
   *                     token:
   *                       type: string
   *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                       description: JWT authentication token
   *                     isNewUser:
   *                       type: boolean
   *                       example: false
   *                       description: Indicates if this was a new signup (true) or login of existing user (false)
   *                     welcomeEmail:
   *                       type: object
   *                       description: Information about the welcome email (only in development environment for new users)
   *                       properties:
   *                         sent:
   *                           type: boolean
   *                           example: false
   *                           description: Whether the welcome email was sent
   *                         message:
   *                           type: string
   *                           example: "Development mode: Welcome email not sent"
   *                           description: Status message about the welcome email
   *                         loginDetails:
   *                           type: object
   *                           description: Login details (only in development environment)
   *                           properties:
   *                             email:
   *                               type: string
   *                               example: "<EMAIL>"
   *                             loginUrl:
   *                               type: string
   *                               example: "http://localhost:3000/login"
   *             examples:
   *               new_user:
   *                 summary: Response for a new user signup
   *                 value:
   *                   error: false
   *                   data:
   *                     user:
   *                       email: "<EMAIL>"
   *                       name:
   *                         first: "John"
   *                         last: "Doe"
   *                       phone: null
   *                       stripeId: "cus_Abc123XyZ456"
   *                       isOwner: true
   *                       platformId: "6821db2a665e6d64674ee897"
   *                     token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                     isNewUser: true
   *                     welcomeEmail:
   *                       sent: false
   *                       message: "Development mode: Welcome email not sent"
   *                       loginDetails:
   *                         email: "<EMAIL>"
   *                         loginUrl: "http://localhost:3000/login"
   *               existing_user:
   *                 summary: Response for an existing user login
   *                 value:
   *                   error: false
   *                   data:
   *                     user:
   *                       email: "<EMAIL>"
   *                       name:
   *                         first: "Jane"
   *                         last: "Smith"
   *                       phone: "9876543210"
   *                       stripeId: "cus_Def456AbC789"
   *                       isOwner: true
   *                       platformId: "6821db2a665e6d64674ee897"
   *                     token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                     isNewUser: false
   *       400:
   *         description: Bad request
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "No Google Access Token Found!!"
   *             examples:
   *               noToken:
   *                 value:
   *                   error: true
   *                   message: "No Google Access Token Found!!"
   */
  async googleSignup(req, res) {
    try {
      const accessToken = req.body.access_token
      if (!accessToken) {
        return res.status(400).json({
          error: true,
          message: "No Google Access Token Found!!",
        })
      }

      // Get user info from Google
      const googleResponse = await axios({
        url: "https://www.googleapis.com/oauth2/v2/userinfo",
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      const googleEmail = googleResponse.data.email
      let user = await User.findOne({
        email: googleEmail,
      }).exec()

      let isNewUser = false

      // If user exists, log them in
      if (user) {
        // Check if user is active
        if (user.isActive === false) {
          return res.status(403).json({
            error: true,
            reason: "There is a problem with your account, please contact support for more details"
          })
        }

        // If the user exists but doesn't have a socialId, update it
        if (!user.socialId) {
          user.socialId = googleResponse.data.id
          user.accountType = "google"
          await user.save()
        }
      } else {
        // User doesn't exist, create a new account
        isNewUser = true
        const { name } = googleResponse.data
        const [first, ...last] = name.split(" ")
        const lastName = last.join(" ")

        user = await User.create({
          email: googleEmail,
          name: {
            first,
            last: lastName,
          },
          socialId: googleResponse.data.id,
          accountType: "google",
          isEmailVerified: true,
          isActive: true,
          userType: "Owner" // Set as owner since this is a new signup
        })

        // Create Stripe customer account for new users
        try {
          if (!user.stripeId || user.stripeId === "") {
            const stripeCustomer = await stripeService.createAccount(user.email)
            user.stripeId = stripeCustomer.id
            await user.save()
          }
        } catch (stripeError) {
          console.error("Stripe account creation error:", stripeError)
          // Don't fail the signup if Stripe account creation fails
          // The user can retry Stripe setup later
        }

        const platform = await Platform.create({
          primaryUser: user._id
        })

        user.platforms = [platform._id]
        await user.save()
      }

      // Generate JWT token for authentication
      const payload = {
        id: user._id,
        _id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        stripeId: user.stripeId,
        userType: user.userType
      }

      const token = jwt.sign(payload, process.env.SECRET, {
        expiresIn: 3600 * 24 * 30, // 1 month
      })

      // Prepare user data based on user type (Owner or TeamMember)
      const userData = {
        email: user.email,
        name: user.name,
        phone: user.phone,
        stripeId: user.stripeId,
        isOwner: user.userType === "Owner"
      }

      // Only include platformId for owners who have platforms
      if (user.userType === "Owner" && user.platforms && user.platforms.length > 0) {
        const [firstPlatform] = user.platforms
        userData.platformId = firstPlatform
      }

      // For team members, include owner details
      if (user.userType === "TeamMember" && user._owner) {
        userData.ownerId = user._owner
      }

      // Prepare response object
      const response = {
        error: false,
        data: {
          user: userData,
          token,
          isNewUser
        }
      }

      // For new users, send welcome email with login details
      if (isNewUser) {
        const loginUrl = `${process.env.SITE_URL}/login`

        // In development environment, don't send email, just return the login details
        if (process.env.NODE_ENV === "development") {
          response.data.welcomeEmail = {
            sent: false,
            message: "Development mode: Welcome email not sent",
            loginDetails: {
              email: user.email,
              loginUrl
            }
          }
        } else {
          // In production, send the welcome email
          try {
            await mail("welcome", {
              to: user.email,
              subject: "Welcome to Your Creator HQ – Let’s Build Something Amazing 🚀",
              locals: {
                firstName: user.name.first,
                user_email: user.email,
                login_link: loginUrl,
                platformName: "Vewmee White Level"
              }
            })
            console.log(`Welcome email sent to Google user ${user.email}`)
          } catch (emailError) {
            console.error("Error sending welcome email to Google user:", emailError)
            // Continue execution even if email fails
          }

          // No email response information added to the API response
        }
      }

      return res.status(200).json(response)
    } catch (error) {
      return res.status(400).json({
        error: true,
        reason: error.message,
      })
    }
  }
}
