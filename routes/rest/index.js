const express = require("express")
const router = express.Router()
const expressJwt = require("express-jwt")
const checkJwt = expressJwt({ secret: process.env.SECRET, algorithms: ["HS256"] }) // the JWT auth check middleware

const { verifyRateLimiter } = require("../../middlewire")

// Import route handlers
const users = require("./users")
const login = require("./auth")
const signup = require("./auth/signup")
const forgotpassword = require("./auth/password")
const platform = require("./platform")
const subscription = require("./subscription")
const domain = require("./domain")
const documents = require("./documents")
const launch = require("./launch")
const account = require("./account")
const webhook = require("./webhook")
const payment = require("./payment")
const migration = require("./migration")
const backendTest = require("./backendTest")

// Unauthenticated routes
router.post("/login", login.post)
router.post("/signup", signup.post)
router.post("/forgotpassword", forgotpassword.startWorkflow)
router.post("/resetpassword", forgotpassword.resetPassword)
router.post("/signup/google", signup.googleSignup)
router.post("/test/fetch/balance", payment.testFetchBalance)

/*
  🔔 Webhook routes (no authentication)
*/
router.post("/webhook/acm/status", domain.webhookStatus)
router.post("/webhook/stripe", webhook.stripe)
router.post("/webhook/account/stripe", webhook.updateStripeAccount)

// Authentication middleware for all subsequent routes
router.all("*", checkJwt)

router.get("/awstempcreds", users.getAwsKey)
// Members routes
router.post("/addMembers", users.addMembers)
router.post("/teamMembers", users.getTeamMembers)
router.put("/teamMember/:id", users.update)
router.put("/teamMember/delete/:id", users.delete)
router.get("/user/:id", users.get)
router.put("/updateProfile", users.updateProfile)

// Platform routes
router.post("/platform/add", platform.addPlatform)
router.get("/platform/list", platform.list)
router.put("/platform/:id", platform.update)
router.post("/platform/comments", platform.addComment)
router.get("/platform/comments", platform.getComments)
router.put("/platform/comments/:id", platform.updateComment)
router.delete("/platform/comments/:id", platform.deleteComment)
router.post("/platform/generatelogo", platform.generateLogo)
router.post("/platform/senddetails", platform.sendPlatformDetails)
router.get("/platform/:id", platform.get)

// Subscription routes
router.post("/card/token", payment.generateToken) // test only
router.post("/payment/card", payment.addCard)
router.get("/payment/cards", payment.fetchCards)
router.post("/payment/card/default", payment.addDefaultCard)
router.post("/payment/setup-fee", payment.paySetupFee)
router.post("/confirm/payment", payment.confirmPayment)
router.post("/make/card/default", payment.setDefaultCard)
router.post("/payment/card/delete", payment.deleteCard)
router.get("/user/fresh/kyc", payment.getInfluencerFreshKycLink)

router.get("/subscription", subscription.get)

// ██████████████████████████████████████████████████████████████████████████████████████
// █▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀█
// █   ████████████████████   DOMAIN ROUTES - MANAGED BY DOMAIN TEAM   ████████████████   █
// █▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄█

/*
  🔍 Domain Lookup & Pricing
*/
router.post("/tld/prices", domain.getTldPrices)
router.post("/domains/search", domain.availableDomains)

/*
  🔁 Renewal Settings
*/
router.post("/update/renewal", domain.updateAutoRenewal)
router.post("/list/domain/validity", domain.listDomainValidity) // Admin Only

/*
  🛡️ Domain Verification
*/
router.post("/domain/details", domain.domainDetails) // Admin Internal Check
router.post("/domain/setup", domain.setup) // External Verification
router.post("/domain/verify/:site", domain.verify) // External Check

/*
  💳 Domain Purchase & Deployment
*/
router.post("/domain/initiate-purchase", domain.initiateDomainPurchase)
router.put("/domain/purchase", domain.purchase)
router.post("/domain/auto/deploy", domain.autoDeploy)

/*
  ☁️ CloudFront Setup
*/
router.post("/domain/cloudfront", domain.cloudfront)

// █▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄█
// █   END OF DOMAIN ROUTES SECTION   ██████████████████████████████████████████████████
// ██████████████████████████████████████████████████████████████████████████████████████

// Document routes
router.post("/documents/add", documents.addDocument)
router.get("/documents/get/:id", documents.getDocuments)
router.post("/documents/suggestion/add", documents.addSuggestion)
router.get("/documents/suggestion/get/:id", documents.getSuggestion)
router.post("/documents/suggestions/addbatch", documents.addBatchSuggestions)
router.put("/documents/suggestion/resolve", documents.resolveSuggestion)

// Feedback routes
router.post("/documents/feedback/add", documents.addFeedback)
router.get("/documents/feedback/get/:id", documents.getFeedback)
router.put("/documents/feedback/resolve/:id", documents.resolveFeedback)

// Launch routes
router.get("/launch", launch.get)
router.post("/launch/review", launch.review)
router.post("/launch/confirm", launch.confirm)

// Account routes
router.get("/me", account.get)
router.put("/account", account.update)
router.get("/account/payments", account.getPaymentHistory)

// Migration routes
router.use("/migration", migration)

// backend api testing routes
router.post("/test/make/vendor", backendTest.makeVendor)

// Test processSubscriptionPayment function
router.post("/test/process-subscription-payment", payment.testProcessSubscriptionPayment)

// Vendor earnings route
router.get("/payment/vendor/earnings", payment.fetchVendorEarnings)

module.exports = router
